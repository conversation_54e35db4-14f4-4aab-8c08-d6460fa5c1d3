[{"id": "resume-api-endpoint", "type": "http in", "z": "resume-processing", "name": "Resume API Endpoint", "url": "/resume-api", "method": "post", "upload": true, "swaggerDoc": "", "x": 150, "y": 120, "wires": [["validate-request"]]}, {"id": "validate-request", "type": "function", "z": "resume-processing", "name": "Validate Request", "func": "if (!msg.req.files || !msg.req.files.resume) {\n    msg.statusCode = 400;\n    msg.payload = { error: \"Missing resume file\" };\n    return [null, msg];\n}\n\nif (!msg.req.body.jobId) {\n    msg.statusCode = 400;\n    msg.payload = { error: \"Missing jobId parameter\" };\n    return [null, msg];\n}\n\nif (!msg.req.body.application) {\n    msg.statusCode = 400;\n    msg.payload = { error: \"Missing application parameter\" };\n    return [null, msg];\n}\n\nconst file = msg.req.files.resume[0];\nconst fileExt = file.originalname.split('.').pop().toLowerCase();\n\nif (!['pdf', 'doc', 'docx'].includes(fileExt)) {\n    msg.statusCode = 400;\n    msg.payload = { error: \"Unsupported file format. Please upload PDF, DOC, or DOCX files only.\" };\n    return [null, msg];\n}\n\nmsg.fileInfo = {\n    path: file.path,\n    originalname: file.originalname,\n    mimetype: file.mimetype,\n    extension: fileExt\n};\n\nmsg.jobId = msg.req.body.jobId;\nmsg.application = msg.req.body.application;\nmsg.candidateId = flow.get('uuid')();\nmsg.startTime = new Date().getTime();\n\nreturn [msg, null];", "outputs": 2, "noerr": 0, "initialize": "// Initialize UUID function\nflow.set('uuid', function() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);\n        return v.toString(16);\n    });\n});", "x": 320, "y": 120, "wires": [["extract-resume-text"], ["http-response"]]}, {"id": "extract-resume-text", "type": "switch", "z": "resume-processing", "name": "File Type", "property": "fileInfo.extension", "propertyType": "msg", "rules": [{"t": "eq", "v": "pdf", "vt": "str"}, {"t": "eq", "v": "doc", "vt": "str"}, {"t": "eq", "v": "docx", "vt": "str"}], "checkall": "false", "x": 500, "y": 120, "wires": [["extract-pdf"], ["extract-doc"], ["extract-doc"]]}, {"id": "extract-pdf", "type": "function", "z": "resume-processing", "name": "Extract PDF Text", "func": "const pdfParse = global.get('pdfParse');\nconst fs = global.get('fs');\n\nreturn new Promise((resolve, reject) => {\n    const dataBuffer = fs.readFileSync(msg.fileInfo.path);\n    \n    pdfParse(dataBuffer).then(data => {\n        msg.resumeText = data.text;\n        node.status({fill:\"green\", shape:\"dot\", text:\"PDF extracted\"});\n        resolve(msg);\n    }).catch(error => {\n        node.error(\"PDF extraction error: \" + error.message);\n        node.status({fill:\"red\", shape:\"ring\", text:\"Extraction failed\"});\n        msg.statusCode = 500;\n        msg.payload = { error: \"Failed to extract text from PDF\" };\n        reject(msg);\n    });\n});", "outputs": 1, "noerr": 0, "initialize": "// Load required modules\nif (!global.get('pdfParse')) {\n    global.set('pdfParse', require('pdf-parse'));\n}\n\nif (!global.get('fs')) {\n    global.set('fs', require('fs-extra'));\n}", "x": 680, "y": 80, "wires": [["get-job-description"]]}, {"id": "extract-doc", "type": "function", "z": "resume-processing", "name": "Extract DOC/DOCX Text", "func": "const mammoth = global.get('mammoth');\nconst fs = global.get('fs');\n\nreturn new Promise((resolve, reject) => {\n    const buffer = fs.readFileSync(msg.fileInfo.path);\n    \n    mammoth.extractRawText({buffer: buffer})\n        .then(result => {\n            msg.resumeText = result.value;\n            node.status({fill:\"green\", shape:\"dot\", text:\"DOC extracted\"});\n            resolve(msg);\n        })\n        .catch(error => {\n            node.error(\"DOC extraction error: \" + error.message);\n            node.status({fill:\"red\", shape:\"ring\", text:\"Extraction failed\"});\n            msg.statusCode = 500;\n            msg.payload = { error: \"Failed to extract text from DOC/DOCX\" };\n            reject(msg);\n        });\n});", "outputs": 1, "noerr": 0, "initialize": "// Load required modules\nif (!global.get('mammoth')) {\n    global.set('mammoth', require('mammoth'));\n}\n\nif (!global.get('fs')) {\n    global.set('fs', require('fs-extra'));\n}", "x": 680, "y": 120, "wires": [["get-job-description"]]}, {"id": "get-job-description", "type": "function", "z": "resume-processing", "name": "Get Job Description", "func": "const AWS = global.get('AWS');\nconst dynamoDB = new AWS.DynamoDB.DocumentClient();\n\nreturn new Promise((resolve, reject) => {\n    const params = {\n        TableName: 'Jobs',\n        Key: {\n            'application': msg.application,\n            'jobId': msg.jobId\n        }\n    };\n    \n    dynamoDB.get(params, (err, data) => {\n        if (err) {\n            node.error(\"DynamoDB error: \" + err);\n            node.status({fill:\"red\", shape:\"ring\", text:\"DB query failed\"});\n            msg.statusCode = 500;\n            msg.payload = { error: \"Failed to retrieve job description\" };\n            reject(msg);\n            return;\n        }\n        \n        if (!data.Item) {\n            node.status({fill:\"yellow\", shape:\"ring\", text:\"Job not found\"});\n            msg.statusCode = 404;\n            msg.payload = { error: \"Job ID not found\" };\n            reject(msg);\n            return;\n        }\n        \n        msg.jobDescription = data.Item;\n        node.status({fill:\"green\", shape:\"dot\", text:\"Job found\"});\n        resolve(msg);\n    });\n});", "outputs": 1, "noerr": 0, "initialize": "// Load AWS SDK\nif (!global.get('AWS')) {\n    const AWS = require('@aws-sdk/client-dynamodb');\n    const { DynamoDBDocument } = require('@aws-sdk/lib-dynamodb');\n    \n    const client = new AWS.DynamoDBClient({\n        region: process.env.AWS_REGION || 'us-east-1',\n        credentials: {\n            accessKeyId: process.env.AWS_ACCESS_KEY_ID,\n            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY\n        }\n    });\n    \n    const dynamoDB = DynamoDBDocument.from(client);\n    global.set('AWS', { DynamoDB: { DocumentClient: dynamoDB } });\n}", "x": 880, "y": 120, "wires": [["analyze-resume"]]}, {"id": "analyze-resume", "type": "function", "z": "resume-processing", "name": "Analyze Resume with OpenAI", "func": "const OpenAI = global.get('OpenAI');\nconst openai = new OpenAI({\n    apiKey: process.env.OPENAI_API_KEY\n});\n\nreturn new Promise(async (resolve, reject) => {\n    try {\n        const prompt = `\nYou are an expert resume analyzer. Analyze the following resume against the job description and provide a structured assessment.\n\nJOB DESCRIPTION:\n${JSON.stringify(msg.jobDescription)}\n\nRESUME TEXT:\n${msg.resumeText}\n\nProvide a detailed analysis in the following JSON format:\n{\n  \"candidateInfo\": {\n    \"name\": \"extracted name\",\n    \"email\": \"extracted email\",\n    \"phone\": \"extracted phone\"\n  },\n  \"experience\": [\n    {\n      \"company\": \"company name\",\n      \"position\": \"job title\",\n      \"duration\": \"time period\",\n      \"responsibilities\": [\"key responsibility 1\", \"key responsibility 2\"]\n    }\n  ],\n  \"education\": [\n    {\n      \"institution\": \"school/university name\",\n      \"degree\": \"degree type\",\n      \"fieldOfStudy\": \"major/field\",\n      \"graduationYear\": year\n    }\n  ],\n  \"skills\": [\"skill1\", \"skill2\", \"skill3\"],\n  \"matchAnalysis\": {\n    \"overallMatchPercentage\": number between 0-100,\n    \"strengths\": [\"strength1\", \"strength2\"],\n    \"concerns\": [\"concern1\", \"concern2\"],\n    \"skillMatching\": {\n      \"requiredSkills\": [\"required skill from job description\"],\n      \"matchedSkills\": [\"skills that match\"],\n      \"missingSkills\": [\"required skills missing from resume\"]\n    }\n  }\n}\n\nEnsure the response is valid JSON with no additional text or explanations outside the JSON structure.`;\n\n        const response = await openai.chat.completions.create({\n            model: \"gpt-4\",\n            messages: [{ role: \"user\", content: prompt }],\n            temperature: 0.2,\n            max_tokens: 2000\n        });\n\n        const content = response.choices[0].message.content.trim();\n        let analysisResult;\n        \n        try {\n            // Extract JSON if it's wrapped in markdown code blocks\n            const jsonMatch = content.match(/```json\\n([\\s\\S]*)\\n```/) || \n                             content.match(/```\\n([\\s\\S]*)\\n```/);\n            \n            if (jsonMatch) {\n                analysisResult = JSON.parse(jsonMatch[1]);\n            } else {\n                analysisResult = JSON.parse(content);\n            }\n            \n            msg.analysisResult = analysisResult;\n            node.status({fill:\"green\", shape:\"dot\", text:\"Analysis complete\"});\n            resolve(msg);\n        } catch (parseError) {\n            node.error(\"JSON parsing error: \" + parseError + \"\\nContent: \" + content);\n            node.status({fill:\"red\", shape:\"ring\", text:\"JSON parse error\"});\n            msg.statusCode = 500;\n            msg.payload = { error: \"Failed to parse analysis result\" };\n            reject(msg);\n        }\n    } catch (error) {\n        node.error(\"OpenAI API error: \" + error.message);\n        node.status({fill:\"red\", shape:\"ring\", text:\"API error\"});\n        msg.statusCode = 500;\n        msg.payload = { error: \"Failed to analyze resume\" };\n        reject(msg);\n    }\n});", "outputs": 1, "noerr": 0, "initialize": "// Load OpenAI SDK\nif (!global.get('OpenAI')) {\n    global.set('OpenAI', require('openai'));\n}", "x": 1080, "y": 120, "wires": [["store-results"]]}, {"id": "store-results", "type": "function", "z": "resume-processing", "name": "Store Results in DynamoDB", "func": "const AWS = global.get('AWS');\nconst dynamoDB = new AWS.DynamoDB.DocumentClient();\n\nreturn new Promise((resolve, reject) => {\n    const timestamp = new Date().toISOString();\n    const processingTime = new Date().getTime() - msg.startTime;\n    \n    const params = {\n        TableName: 'LCPCandidate',\n        Item: {\n            'application': msg.application,\n            'candidateid': msg.candidateId,\n            'Job_id': msg.jobId,\n            'analysisResult': msg.analysisResult,\n            'timestamp': timestamp,\n            'resumeText': msg.resumeText,\n            'processingTimeMs': processingTime\n        }\n    };\n    \n    dynamoDB.put(params, (err) => {\n        if (err) {\n            node.error(\"DynamoDB error: \" + err);\n            node.status({fill:\"red\", shape:\"ring\", text:\"DB save failed\"});\n            msg.statusCode = 500;\n            msg.payload = { error: \"Failed to store analysis results\" };\n            reject(msg);\n            return;\n        }\n        \n        // Prepare success response\n        msg.statusCode = 200;\n        msg.payload = {\n            candidateId: msg.candidateId,\n            analysisResult: msg.analysisResult,\n            processingTimeMs: processingTime\n        };\n        \n        node.status({fill:\"green\", shape:\"dot\", text:\"Results stored\"});\n        resolve(msg);\n    });\n});", "outputs": 1, "noerr": 0, "x": 1280, "y": 120, "wires": [["http-response"]]}, {"id": "http-response", "type": "http response", "z": "resume-processing", "name": "HTTP Response", "statusCode": "", "headers": {"content-type": "application/json"}, "x": 1470, "y": 120, "wires": []}, {"id": "catch-errors", "type": "catch", "z": "resume-processing", "name": "Catch Errors", "scope": null, "uncaught": false, "x": 1080, "y": 200, "wires": [["format-error"]]}, {"id": "format-error", "type": "function", "z": "resume-processing", "name": "Format Error Response", "func": "if (!msg.statusCode) {\n    msg.statusCode = 500;\n}\n\nif (!msg.payload || !msg.payload.error) {\n    msg.payload = { \n        error: msg.error?.message || \"An unexpected error occurred\"\n    };\n}\n\nnode.status({fill:\"red\", shape:\"ring\", text:msg.payload.error});\n\nreturn msg;", "outputs": 1, "noerr": 0, "x": 1280, "y": 200, "wires": [["http-response"]]}, {"id": "debug-flow", "type": "debug", "z": "resume-processing", "name": "Debug Flow", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1080, "y": 60, "wires": []}]